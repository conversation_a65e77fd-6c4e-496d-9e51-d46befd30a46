<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧食堂平台 - 专业的校园食堂管理系统</title>
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">

    <!-- 引入系统CSS框架 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- CSP Nonce -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'nonce-{{ csp_nonce }}'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';">
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top shadow">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand fw-bold fs-4" href="#">
                <i class="fa fa-cutlery me-2"></i>
                智慧食堂平台
            </a>

            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">核心功能</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">联系我们</a>
                    </li>
                </ul>

                <!-- 登录注册按钮 -->
                <div class="d-flex gap-2">
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-light">登录</a>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-light text-primary fw-bold">免费注册</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主横幅 -->
    <section class="bg-primary text-white" style="padding-top: 120px; padding-bottom: 80px;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">智慧食堂管理平台</h1>
                    <p class="lead mb-4">
                        专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯，
                        为校园食堂管理提供高效便捷的技术支持。
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-lg text-primary fw-bold">
                            <i class="fa fa-user-plus me-2"></i>免费注册
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg">
                            <i class="fa fa-info-circle me-2"></i>了解更多
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="p-4">
                        <i class="fa fa-cutlery display-1 text-white-50"></i>
                        <div class="mt-3">
                            <span class="badge bg-light text-primary fs-6 px-3 py-2">
                                <i class="fa fa-gift me-1"></i>永久免费使用
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 核心功能 -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-dark mb-3">核心功能</h2>
                <p class="lead text-muted">八大智能化功能，全面保障食堂安全</p>
            </div>

            <div class="row g-4">
                <!-- 功能卡片1 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="card-body text-center p-4">
                            <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-shield text-primary fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">食品安全管理</h5>
                            <p class="card-text text-muted">
                                全程追溯，确保食品安全。从食材采购到餐后服务，全方位保障食品安全。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片2 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="card-body text-center p-4">
                            <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-shopping-cart text-success fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">智能采购系统</h5>
                            <p class="card-text text-muted">
                                智能价格对比，自动生成采购单，简化采购流程，提升采购效率。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片3 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="card-body text-center p-4">
                            <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-exchange text-info fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">出入库管理</h5>
                            <p class="card-text text-muted">
                                完整管理出入库流程，自动生成台账报表，实时监控库存情况。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片4 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="card-body text-center p-4">
                            <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-list text-warning fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">灵活菜单管理</h5>
                            <p class="card-text text-muted">
                                支持周菜单灵活安排，一键生成带价格及营养分析的带量食谱。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片5 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="card-body text-center p-4">
                            <div class="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-search text-danger fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">智能检查系统</h5>
                            <p class="card-text text-muted">
                                员工扫码上传食堂状况，管理员在线评价反馈，实时监控运营状态。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片6 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="card-body text-center p-4">
                            <div class="bg-secondary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-users text-secondary fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">家校共陪餐</h5>
                            <p class="card-text text-muted">
                                邀请家长参与陪餐体验，提升食堂管理透明度，加强家校互动沟通。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- 联系我们 -->
    <section id="contact" class="bg-light py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-dark mb-3">联系我们</h2>
                <p class="lead text-muted">专业的技术团队为您提供7×24小时服务支持</p>
            </div>

            <div class="row g-4">
                <!-- 联系信息 -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <h5 class="card-title fw-bold mb-4">
                                <i class="fa fa-phone text-primary me-2"></i>联系方式
                            </h5>

                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-phone text-primary me-3"></i>
                                    <div>
                                        <small class="text-muted">电话咨询</small>
                                        <div class="fw-bold">18373062333</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-envelope text-primary me-3"></i>
                                    <div>
                                        <small class="text-muted">邮件咨询</small>
                                        <div class="fw-bold"><EMAIL></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-map-marker text-primary me-3"></i>
                                    <div>
                                        <small class="text-muted">服务地区</small>
                                        <div class="fw-bold">湖南.岳阳</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 免费使用标签 -->
                            <div class="alert alert-success border-0 mt-4">
                                <div class="d-flex align-items-center">
                                    <i class="fa fa-gift me-2"></i>
                                    <div>
                                        <div class="fw-bold">免费使用！永不停机</div>
                                        <small>专业技术支持 · 7×24小时服务</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 在线咨询表单 -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <h5 class="card-title fw-bold mb-4">
                                <i class="fa fa-comments text-primary me-2"></i>在线咨询
                            </h5>

                            <form id="consultationForm" method="POST" action="{{ url_for('consultation.submit_consultation') }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">姓名</label>
                                        <input type="text" class="form-control" name="name" placeholder="请输入您的姓名" required minlength="2" maxlength="50">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">联系方式类型</label>
                                        <select class="form-select" name="contact_type" required>
                                            <option value="微信">微信</option>
                                            <option value="电话">电话</option>
                                            <option value="邮箱">邮箱</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">联系方式</label>
                                        <input type="text" class="form-control" name="contact_value" placeholder="请输入您的联系方式" required minlength="3" maxlength="100">
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">咨询内容</label>
                                        <textarea class="form-control" name="content" rows="4" placeholder="请详细描述您的需求，包括学校规模、具体功能需求等" required minlength="10" maxlength="1000"></textarea>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fa fa-paper-plane me-2"></i>提交咨询
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fa fa-cutlery me-2"></i>
                        <span class="fw-bold">智慧食堂平台</span>
                    </div>
                    <small class="text-muted">专注校园食堂智能化管理解决方案</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">© 2025 智慧食堂平台. All rights reserved.</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
</body>
</body>
</html>