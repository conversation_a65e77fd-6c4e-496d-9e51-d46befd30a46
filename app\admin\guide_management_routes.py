#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新用户引导管理路由
管理员后台管理新用户引导系统
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, current_app
from flask_login import login_required, current_user
from app.utils import admin_required
from app.services.user_guide_service import UserGuideService
# from app.services.video_guide_service import VideoGuideService  # 不再使用
from app.services.scenario_guide_service import ScenarioGuideService
from app.models import User, AdministrativeArea
from app import db
from datetime import datetime, timedelta
from sqlalchemy import func, text
import json

guide_mgmt_bp = Blueprint('guide_management', __name__, url_prefix='/admin/guide')

@guide_mgmt_bp.route('/')
@login_required
@admin_required
def index():
    """引导管理首页"""
    return redirect(url_for('guide_management.dashboard'))

@guide_mgmt_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """引导管理仪表盘"""
    try:
        # 获取引导使用统计
        stats = get_guide_statistics()

        # 获取最近的引导活动
        recent_activities = get_recent_guide_activities()

        return render_template('admin/guide_management/dashboard.html',
                             title='新用户引导管理',
                             stats=stats,
                             recent_activities=recent_activities)
    except Exception as e:
        current_app.logger.error(f'加载引导管理数据失败: {str(e)}')
        flash(f'加载引导管理数据失败: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

@guide_mgmt_bp.route('/users')
@login_required
@admin_required
def user_guide_status():
    """用户引导状态管理"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取搜索参数
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    school_type = request.args.get('school_type', '')

    # 构建查询
    query = User.query.join(AdministrativeArea, User.area_id == AdministrativeArea.id)

    if search:
        query = query.filter(
            db.or_(
                User.username.contains(search),
                User.real_name.contains(search),
                AdministrativeArea.name.contains(search)
            )
        )

    # 添加排序（SQL Server 分页要求）- 使用主键确保唯一性
    query = query.order_by(User.id.desc())

    # 分页
    users = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取每个用户的引导状态
    user_guide_data = []
    for user in users.items:
        guide_status = UserGuideService.get_user_guide_status(user.id)
        user_data = {
            'user': user,
            'guide_status': guide_status,
            'school_type': detect_user_school_type(user)
        }
        user_guide_data.append(user_data)

    return render_template('admin/guide_management/users.html',
                         title='用户引导状态',
                         users=users,
                         user_guide_data=user_guide_data,
                         search=search,
                         status_filter=status_filter,
                         school_type=school_type)

@guide_mgmt_bp.route('/scenarios')
@login_required
@admin_required
def scenario_management():
    """场景管理"""
    scenarios = ScenarioGuideService.SCHOOL_TYPES
    scenario_guides = ScenarioGuideService.SCENARIO_GUIDES

    # 获取每种场景的使用统计
    scenario_stats = {}
    for scenario_type in scenarios.keys():
        count = get_scenario_usage_count(scenario_type)
        scenario_stats[scenario_type] = count

    return render_template('admin/guide_management/scenarios.html',
                         title='场景管理',
                         scenarios=scenarios,
                         scenario_guides=scenario_guides,
                         scenario_stats=scenario_stats)

@guide_mgmt_bp.route('/videos')
@login_required
@admin_required
def video_management():
    """视频资源管理"""
    from app.models import VideoGuide
    from app.forms.video_guide import VideoGuideForm

    # 获取所有视频（只从数据库获取真实数据）
    videos = VideoGuide.query.order_by(VideoGuide.step_name, VideoGuide.created_at.desc()).all()

    # 按步骤分组（只显示有视频的步骤）
    video_resources = {}
    step_names = {
        'daily_management': '日常管理模块',
        'suppliers': '供应商管理',
        'ingredients_recipes': '食材食谱管理',
        'weekly_menu': '周菜单制定',
        'purchase_order': '采购订单管理',
        'stock_in': '食材入库管理',
        'consumption_plan': '消耗量计划',
        'stock_out': '食材出库管理',
        'traceability': '食材溯源管理',
        'food_samples': '留样记录管理'
    }

    # 只显示数据库中实际存在视频的步骤
    for step_name, step_title in step_names.items():
        step_videos = [v for v in videos if v.step_name == step_name]
        if step_videos:  # 只有当该步骤有视频时才显示
            video_resources[step_name] = {
                'title': step_title,
                'duration': f"{len(step_videos)}个视频",
                'videos': [v.to_dict() for v in step_videos]
            }

    form = VideoGuideForm()

    return render_template('admin/video_management.html',
                         title='视频资源管理',
                         video_resources=video_resources,
                         form=form)

@guide_mgmt_bp.route('/videos/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_video():
    """创建视频"""
    from app.models import VideoGuide
    from app.forms.video_guide import VideoGuideForm
    from sqlalchemy import text
    import os
    import uuid
    from werkzeug.utils import secure_filename

    form = VideoGuideForm()

    if form.validate_on_submit():
        try:
            # 处理视频文件上传
            video_file = form.video_file.data
            filename = secure_filename(video_file.filename)
            unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}_{filename}"

            # 确保上传目录存在
            upload_folder = os.path.join(current_app.static_folder, 'videos', form.step_name.data)
            os.makedirs(upload_folder, exist_ok=True)

            # 保存视频文件
            file_path = os.path.join(upload_folder, unique_filename)
            video_file.save(file_path)

            # 生成相对路径
            relative_path = f"/static/videos/{form.step_name.data}/{unique_filename}"

            # 处理缩略图（如果有）
            thumbnail_path = None
            if form.thumbnail_file.data:
                thumbnail_file = form.thumbnail_file.data
                thumbnail_filename = secure_filename(thumbnail_file.filename)
                thumbnail_unique = f"thumb_{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}_{thumbnail_filename}"
                thumbnail_file_path = os.path.join(upload_folder, thumbnail_unique)
                thumbnail_file.save(thumbnail_file_path)
                thumbnail_path = f"/static/videos/{form.step_name.data}/{thumbnail_unique}"

            # 使用原始SQL创建记录
            sql = text("""
                INSERT INTO video_guides
                (step_name, name, description, file_path, thumbnail)
                OUTPUT inserted.id
                VALUES
                (:step_name, :name, :description, :file_path, :thumbnail)
            """)

            params = {
                'step_name': form.step_name.data,
                'name': form.name.data,
                'description': form.description.data or '',
                'file_path': relative_path,
                'thumbnail': thumbnail_path
            }

            result = db.session.execute(sql, params)
            video_id = result.fetchone()[0]
            db.session.commit()

            flash('视频创建成功', 'success')
            return redirect(url_for('guide_management.video_management'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建视频失败: {str(e)}")
            flash(f'创建视频失败: {str(e)}', 'danger')

    return render_template('admin/video_guide/create.html', form=form)

@guide_mgmt_bp.route('/videos/upload', methods=['POST'])
@login_required
@admin_required
def upload_video():
    """AJAX上传视频"""
    from app.models import VideoGuide
    from sqlalchemy import text
    import os
    import uuid
    from werkzeug.utils import secure_filename

    try:
        # 检查是否有文件
        if 'video' not in request.files:
            return jsonify({'success': False, 'message': '没有上传视频文件'}), 400

        video_file = request.files['video']
        if not video_file.filename:
            return jsonify({'success': False, 'message': '没有选择视频文件'}), 400

        # 获取表单数据
        step_name = request.form.get('step_name')
        video_name = request.form.get('video_name')
        description = request.form.get('description', '')

        if not step_name or not video_name:
            return jsonify({'success': False, 'message': '请填写完整的视频信息'}), 400

        # 检查文件类型
        allowed_extensions = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'}
        file_extension = video_file.filename.rsplit('.', 1)[1].lower() if '.' in video_file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'success': False, 'message': '不支持的视频格式'}), 400

        # 检查文件大小（限制为50MB）
        max_size = 50 * 1024 * 1024  # 50MB
        video_file.seek(0, 2)
        file_size = video_file.tell()
        video_file.seek(0)

        if file_size > max_size:
            return jsonify({'success': False, 'message': '视频文件大小不能超过50MB'}), 400

        # 生成安全的文件名
        filename = secure_filename(video_file.filename)
        unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}_{filename}"

        # 确保上传目录存在
        upload_folder = os.path.join(current_app.static_folder, 'videos', step_name)
        os.makedirs(upload_folder, exist_ok=True)

        # 保存视频文件
        file_path = os.path.join(upload_folder, unique_filename)
        video_file.save(file_path)

        # 生成相对路径
        relative_path = f"/static/videos/{step_name}/{unique_filename}"

        # 处理缩略图（如果有）
        thumbnail_path = None
        if 'thumbnail_file' in request.files and request.files['thumbnail_file'].filename:
            thumbnail_file = request.files['thumbnail_file']
            thumbnail_filename = secure_filename(thumbnail_file.filename)
            thumbnail_unique = f"thumb_{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}_{thumbnail_filename}"
            thumbnail_file_path = os.path.join(upload_folder, thumbnail_unique)
            thumbnail_file.save(thumbnail_file_path)
            thumbnail_path = f"/static/videos/{step_name}/{thumbnail_unique}"

        # 使用原始SQL创建记录
        sql = text("""
            INSERT INTO video_guides
            (step_name, name, description, file_path, thumbnail)
            OUTPUT inserted.id
            VALUES
            (:step_name, :name, :description, :file_path, :thumbnail)
        """)

        params = {
            'step_name': step_name,
            'name': video_name,
            'description': description,
            'file_path': relative_path,
            'thumbnail': thumbnail_path
        }

        result = db.session.execute(sql, params)
        video_id = result.fetchone()[0]
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '视频上传成功',
            'data': {
                'id': video_id,
                'step_name': step_name,
                'name': video_name,
                'description': description,
                'file_path': relative_path,
                'thumbnail': thumbnail_path
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'视频上传失败: {str(e)}')
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'}), 500

@guide_mgmt_bp.route('/videos/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_video(id):
    """编辑视频"""
    from app.models import VideoGuide
    from sqlalchemy import text
    import os
    import uuid
    from werkzeug.utils import secure_filename
    from datetime import datetime

    video = VideoGuide.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # 获取表单数据
            video_name = request.form.get('video_name')
            description = request.form.get('description', '')

            if not video_name:
                return jsonify({
                    'success': False,
                    'message': '视频名称不能为空'
                }), 400

            # 更新基本信息
            params = {
                'id': id,
                'name': video_name,
                'description': description
            }

            # 处理新视频文件（如果有）
            video_file = request.files.get('video')
            if video_file and video_file.filename:
                # 验证文件类型
                allowed_extensions = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'}
                file_extension = video_file.filename.rsplit('.', 1)[1].lower() if '.' in video_file.filename else ''

                if file_extension not in allowed_extensions:
                    return jsonify({
                        'success': False,
                        'message': f'不支持的视频格式。支持的格式：{", ".join(allowed_extensions)}'
                    }), 400

                # 删除旧文件
                if video.file_path and os.path.exists(os.path.join(current_app.static_folder, video.file_path.lstrip('/'))):
                    try:
                        os.remove(os.path.join(current_app.static_folder, video.file_path.lstrip('/')))
                    except:
                        pass

                # 保存新文件
                filename = secure_filename(video_file.filename)
                unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}_{filename}"

                upload_folder = os.path.join(current_app.static_folder, 'videos', video.step_name)
                os.makedirs(upload_folder, exist_ok=True)

                file_path = os.path.join(upload_folder, unique_filename)
                video_file.save(file_path)

                relative_path = f"/static/videos/{video.step_name}/{unique_filename}"
                params['file_path'] = relative_path

            # 构建SQL更新语句
            update_fields = ['name = :name', 'description = :description']
            if 'file_path' in params:
                update_fields.append('file_path = :file_path')

            sql = text(f"""
                UPDATE video_guides
                SET {', '.join(update_fields)}
                WHERE id = :id
            """)

            db.session.execute(sql, params)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '视频更新成功'
            })

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新视频失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'更新失败: {str(e)}'
            }), 500

    # GET请求返回编辑页面（如果需要的话）
    return jsonify({
        'success': False,
        'message': '不支持的请求方法'
    }), 405

@guide_mgmt_bp.route('/videos/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_video(id):
    """删除视频"""
    from app.models import VideoGuide
    from sqlalchemy import text
    import os

    try:
        video = VideoGuide.query.get_or_404(id)

        # 删除文件
        if video.file_path:
            file_path = os.path.join(current_app.static_folder, video.file_path.lstrip('/'))
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    current_app.logger.info(f'视频文件已删除: {file_path}')
                except Exception as e:
                    current_app.logger.error(f'删除视频文件失败: {str(e)}')

        # 删除缩略图
        if video.thumbnail:
            thumbnail_path = os.path.join(current_app.static_folder, video.thumbnail.lstrip('/'))
            if os.path.exists(thumbnail_path):
                try:
                    os.remove(thumbnail_path)
                    current_app.logger.info(f'缩略图已删除: {thumbnail_path}')
                except Exception as e:
                    current_app.logger.error(f'删除缩略图失败: {str(e)}')

        # 删除数据库记录
        sql = text("DELETE FROM video_guides WHERE id = :id")
        db.session.execute(sql, {'id': id})
        db.session.commit()

        if request.is_json:
            return jsonify({'success': True, 'message': '视频删除成功'})
        else:
            flash('视频删除成功', 'success')
            return redirect(url_for('guide_management.video_management'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除视频失败: {str(e)}')
        if request.is_json:
            return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 500
        else:
            flash(f'删除视频失败: {str(e)}', 'danger')
            return redirect(url_for('guide_management.video_management'))

@guide_mgmt_bp.route('/videos/test', methods=['GET', 'POST'])
@login_required
@admin_required
def test_video_route():
    """测试视频路由"""
    current_app.logger.info('测试路由被访问')
    return jsonify({
        'success': True,
        'message': '测试路由正常工作',
        'method': request.method
    })

@guide_mgmt_bp.route('/videos/update', methods=['POST'])
@login_required
@admin_required
def update_video():
    """更新引导视频信息"""
    try:
        step_name = request.form.get('step_name')
        new_video_name = request.form.get('video_name')
        description = request.form.get('description', '')

        current_app.logger.info(f'更新视频信息: step_name={step_name}, video_name={new_video_name}, description={description}')

        if not step_name or not new_video_name:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        # 检查是否有新的视频文件上传
        video_file = request.files.get('video')
        if video_file and video_file.filename and video_file.filename.strip():
            # 有文件上传，处理文件上传
            import os
            from werkzeug.utils import secure_filename
            import uuid
            from datetime import datetime

            # 验证文件类型
            allowed_extensions = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'}
            file_extension = video_file.filename.rsplit('.', 1)[1].lower() if '.' in video_file.filename else ''

            if file_extension not in allowed_extensions:
                return jsonify({
                    'success': False,
                    'message': f'不支持的视频格式。支持的格式：{", ".join(allowed_extensions)}'
                }), 400

            # 检查文件大小
            max_size = 100 * 1024 * 1024  # 100MB
            video_file.seek(0, 2)
            file_size = video_file.tell()
            video_file.seek(0)

            if file_size > max_size:
                return jsonify({
                    'success': False,
                    'message': '视频文件大小不能超过100MB'
                }), 400

            # 确保上传目录存在
            upload_folder = os.path.join(
                current_app.static_folder,
                'videos',
                step_name
            )
            os.makedirs(upload_folder, exist_ok=True)

            # 生成新文件名
            filename = secure_filename(video_file.filename)
            unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}_{filename}"

            # 保存视频文件
            file_path = os.path.join(upload_folder, unique_filename)
            video_file.save(file_path)

            # 生成相对路径
            relative_path = f"/static/videos/{step_name}/{unique_filename}"

            current_app.logger.info(f'视频文件已更新: {file_path}')

            return jsonify({
                'success': True,
                'message': '视频信息和文件更新成功',
                'data': {
                    'step_name': step_name,
                    'video_name': new_video_name,
                    'description': description,
                    'file_path': relative_path,
                    'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            })
        else:
            # 只更新视频信息，不更换文件
            return jsonify({
                'success': True,
                'message': '视频信息更新成功',
                'data': {
                    'step_name': step_name,
                    'video_name': new_video_name,
                    'description': description,
                    'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            })

    except Exception as e:
        current_app.logger.error(f'更新视频失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

@guide_mgmt_bp.route('/videos/details', methods=['GET'])
@login_required
@admin_required
def get_video_details():
    """获取视频详细信息"""
    try:
        step_name = request.args.get('step_name')
        video_name = request.args.get('video_name')

        if not step_name or not video_name:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        # 这里可以从数据库或配置文件中获取视频详情
        # 目前返回基本信息，后续可以扩展
        video_details = {
            'step_name': step_name,
            'video_name': video_name,
            'description': '',  # 可以从数据库获取
            'duration': '',     # 可以从文件信息获取
            'file_size': '',    # 可以从文件信息获取
            'created_at': '',   # 可以从文件信息获取
        }

        # 尝试从文件系统获取一些基本信息
        import os
        video_folder = os.path.join(
            current_app.static_folder,
            'videos',
            'guide',
            step_name
        )

        if os.path.exists(video_folder):
            for filename in os.listdir(video_folder):
                if video_name in filename:
                    file_path = os.path.join(video_folder, filename)
                    if os.path.exists(file_path):
                        # 获取文件大小
                        file_size = os.path.getsize(file_path)
                        video_details['file_size'] = f"{file_size / (1024*1024):.1f} MB"

                        # 获取创建时间
                        import time
                        created_time = os.path.getctime(file_path)
                        video_details['created_at'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(created_time))
                        break

        return jsonify({
            'success': True,
            'video': video_details
        })

    except Exception as e:
        current_app.logger.error(f'获取视频详情失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取详情失败: {str(e)}'
        }), 500

@guide_mgmt_bp.route('/demo-data')
@login_required
@admin_required
def demo_data_management():
    """演示数据管理"""
    # 获取演示数据统计
    demo_stats = get_demo_data_statistics()

    return render_template('admin/guide_management/demo_data.html',
                         title='演示数据管理',
                         demo_stats=demo_stats)

@guide_mgmt_bp.route('/settings')
@login_required
@admin_required
def guide_settings():
    """引导系统设置"""
    # 获取当前设置
    settings = get_guide_system_settings()

    return render_template('admin/guide_management/settings.html',
                         title='引导系统设置',
                         settings=settings)

# API 路由
@guide_mgmt_bp.route('/api/reset-user-guide/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def reset_user_guide(user_id):
    """重置用户引导状态"""
    try:
        user = User.query.get_or_404(user_id)
        UserGuideService.reset_guide(user_id)

        return jsonify({
            'success': True,
            'message': f'用户 {user.username} 的引导状态已重置'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_mgmt_bp.route('/api/force-complete-guide/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def force_complete_guide(user_id):
    """强制完成用户引导"""
    try:
        user = User.query.get_or_404(user_id)
        UserGuideService.complete_step(user_id, 'completed')

        return jsonify({
            'success': True,
            'message': f'用户 {user.username} 的引导已标记为完成'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_mgmt_bp.route('/api/clean-demo-data/<int:area_id>', methods=['POST'])
@login_required
@admin_required
def clean_demo_data(area_id):
    """清理指定区域的演示数据"""
    try:
        area = AdministrativeArea.query.get_or_404(area_id)
        result = clean_area_demo_data(area_id)

        return jsonify({
            'success': True,
            'message': f'区域 {area.name} 的演示数据已清理',
            'cleaned_count': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_mgmt_bp.route('/api/bulk-reset-guides', methods=['POST'])
@login_required
@admin_required
def bulk_reset_guides():
    """批量重置引导状态"""
    try:
        user_ids = request.json.get('user_ids', [])
        if not user_ids:
            return jsonify({
                'success': False,
                'message': '请选择要重置的用户'
            }), 400

        reset_count = 0
        for user_id in user_ids:
            try:
                UserGuideService.reset_guide(user_id)
                reset_count += 1
            except:
                continue

        return jsonify({
            'success': True,
            'message': f'成功重置 {reset_count} 个用户的引导状态'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 辅助函数
def get_guide_statistics():
    """获取引导使用统计"""
    try:
        # 这里使用session数据统计，实际项目中可能需要数据库存储
        total_users = User.query.count()
        new_users_today = User.query.filter(
            func.date(User.created_at) == datetime.now().date()
        ).count()

        # 模拟引导完成统计
        stats = {
            'total_users': total_users,
            'new_users_today': new_users_today,
            'guide_started': int(total_users * 0.7),  # 假设70%的用户开始了引导
            'guide_completed': int(total_users * 0.5),  # 假设50%的用户完成了引导
            'completion_rate': 0.71,  # 完成率
            'average_time': '18分钟',  # 平均完成时间
            'most_popular_step': 'weekly_menu',  # 最受欢迎的步骤
            'drop_off_step': 'purchase_order'  # 最容易放弃的步骤
        }

        return stats
    except Exception as e:
        # 返回默认值而不是空字典，确保所有必需的字段都存在
        return {
            'total_users': 0,
            'new_users_today': 0,
            'guide_started': 0,
            'guide_completed': 0,
            'completion_rate': 0.0,
            'average_time': '0分钟',
            'most_popular_step': '暂无',
            'drop_off_step': '暂无'
        }

def get_recent_guide_activities():
    """获取最近的引导活动"""
    try:
        # 获取最近注册的用户作为引导活动
        recent_users = User.query.filter(
            User.created_at >= datetime.now() - timedelta(days=7)
        ).order_by(User.created_at.desc()).limit(10).all()

        activities = []
        for user in recent_users:
            activities.append({
                'user': user,
                'action': '开始引导',
                'time': user.created_at,
                'school_type': detect_user_school_type(user)
            })

        return activities
    except Exception as e:
        return []

def detect_user_school_type(user):
    """检测用户学校类型"""
    if user.area:
        return ScenarioGuideService.detect_school_type(user.area.name)
    return 'unknown'

def get_scenario_usage_count(scenario_type):
    """获取场景使用次数"""
    # 这里可以从数据库或日志中统计
    # 暂时返回模拟数据
    usage_map = {
        'primary': 45,
        'middle': 32,
        'high': 28,
        'vocational': 15,
        'university': 8,
        'rural': 22
    }
    return usage_map.get(scenario_type, 0)

def get_demo_data_statistics():
    """获取演示数据统计"""
    try:
        from app.models import Supplier, Ingredient, Recipe

        # 统计包含"演示"或"demo"的数据
        demo_suppliers = Supplier.query.filter(
            db.or_(
                Supplier.name.contains('演示'),
                Supplier.name.contains('demo'),
                Supplier.notes.contains('演示数据')
            )
        ).count()

        demo_ingredients = Ingredient.query.filter(
            db.or_(
                Ingredient.name.contains('演示'),
                Ingredient.notes.contains('演示数据')
            )
        ).count()

        demo_recipes = Recipe.query.filter(
            db.or_(
                Recipe.name.contains('演示'),
                Recipe.description.contains('演示数据')
            )
        ).count()

        return {
            'suppliers': demo_suppliers,
            'ingredients': demo_ingredients,
            'recipes': demo_recipes,
            'total': demo_suppliers + demo_ingredients + demo_recipes
        }
    except Exception as e:
        return {'suppliers': 0, 'ingredients': 0, 'recipes': 0, 'total': 0}

def get_guide_system_settings():
    """获取引导系统设置"""
    # 这里可以从配置文件或数据库中读取设置
    return {
        'auto_start_guide': True,
        'show_welcome_message': True,
        'enable_video_demos': True,
        'enable_scenario_detection': True,
        'demo_data_retention_days': 30,
        'guide_timeout_minutes': 30
    }

def clean_area_demo_data(area_id):
    """清理指定区域的演示数据"""
    try:
        from app.models import Supplier, Ingredient, Recipe

        cleaned_count = 0

        # 清理演示供应商
        demo_suppliers = Supplier.query.filter(
            Supplier.area_id == area_id,
            db.or_(
                Supplier.name.contains('演示'),
                Supplier.name.contains('demo'),
                Supplier.notes.contains('演示数据')
            )
        ).all()

        for supplier in demo_suppliers:
            db.session.delete(supplier)
            cleaned_count += 1

        # 清理演示食材
        demo_ingredients = Ingredient.query.filter(
            db.or_(
                Ingredient.name.contains('演示'),
                Ingredient.notes.contains('演示数据')
            )
        ).all()

        for ingredient in demo_ingredients:
            db.session.delete(ingredient)
            cleaned_count += 1

        db.session.commit()
        return cleaned_count

    except Exception as e:
        db.session.rollback()
        raise e
