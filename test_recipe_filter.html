<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>食谱筛选功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>食谱筛选功能测试</h2>
        <p>请访问以下链接测试学校/系统食谱筛选功能：</p>
        
        <div class="alert alert-info">
            <h5>测试步骤：</h5>
            <ol>
                <li>访问周菜单计划页面</li>
                <li>点击任意表格单元格（日期+餐次）</li>
                <li>在弹出的模态框中查看左侧的食谱来源筛选按钮</li>
                <li>测试筛选功能：
                    <ul>
                        <li>🏫 学校食谱 - 只显示本校食谱</li>
                        <li>🌍 系统食谱 - 只显示系统食谱</li>
                        <li>📋 全部食谱 - 显示所有食谱</li>
                    </ul>
                </li>
                <li>验证食谱卡片上的标识：
                    <ul>
                        <li>系统食谱显示 <i class="fas fa-globe"></i> 系统</li>
                        <li>学校食谱显示 <i class="fas fa-school"></i> 学校</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试链接</h5>
                    </div>
                    <div class="card-body">
                        <a href="http://127.0.0.1:5000/weekly-menu/plan?area_id=42&week_start=2025-06-02" 
                           class="btn btn-primary btn-lg" target="_blank">
                            <i class="fas fa-calendar-alt"></i> 打开周菜单计划页面
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>功能特点</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> 学校级数据隔离</li>
                            <li><i class="fas fa-check text-success"></i> 智能筛选功能</li>
                            <li><i class="fas fa-check text-success"></i> 视觉标识区分</li>
                            <li><i class="fas fa-check text-success"></i> 状态保持</li>
                            <li><i class="fas fa-check text-success"></i> 本地筛选性能</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <div class="alert alert-success">
                <h5><i class="fas fa-lightbulb"></i> 实现亮点：</h5>
                <ul class="mb-0">
                    <li><strong>后端数据隔离</strong>：修改了食谱查询逻辑，实现学校级数据隔离</li>
                    <li><strong>前端筛选UI</strong>：在模态框中添加了美观的筛选按钮组</li>
                    <li><strong>智能标识</strong>：食谱卡片自动显示来源标识（学校/系统）</li>
                    <li><strong>状态管理</strong>：筛选状态在分类切换时保持，模态框重置时恢复</li>
                    <li><strong>性能优化</strong>：使用本地筛选，响应速度快</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
